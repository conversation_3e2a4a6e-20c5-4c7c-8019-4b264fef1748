from flask import Flask, render_template, request, jsonify

app = Flask(__name__)

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/properties', methods=['GET'])
def get_properties():
    # This will eventually fetch properties from a database
    properties = [
        {"id": 1, "name": "Residential House 1", "type": "residential", "address": "123 Main St"},
        {"id": 2, "name": "Commercial Building A", "type": "commercial", "address": "456 Business Ave"},
        {"id": 3, "name": "Apartment Unit 101", "type": "apartment", "address": "789 Apt Complex"}
    ]
    return jsonify(properties)

if __name__ == '__main__':
    app.run(debug=True)