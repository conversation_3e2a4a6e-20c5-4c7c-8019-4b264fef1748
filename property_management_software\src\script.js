document.getElementById('getStarted').addEventListener('click', () => {
    alert('Welcome! More features coming soon.');
    // In a real application, this would navigate to a dashboard or login page
});

// Example of fetching data from the backend
fetch('/api/properties')
    .then(response => response.json())
    .then(data => {
        console.log('Properties from backend:', data);
        // You can display these properties on the page
    })
    .catch(error => console.error('Error fetching properties:', error));